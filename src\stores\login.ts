/**
 * 登录相关 Store
 */

import { defineStore } from "pinia";
import { showToast } from "vant";
import router from "@/router";
import { useGlobalStore } from "@/stores/global";
import { VerificationMgr, VERIFICATION_TYPE } from "@/utils/VerificationMgr";
import { Md5 } from "@/utils/core/Md5";
import { setLocalStorage, getLocalStorage } from "@/utils/core/Storage";
import { loginManager } from "@/utils/managers/LoginManager";
import { showZLoading, closeZLoading } from "@/utils/ZLoadingAPI";
import { LoginMgr } from "@/utils/ThirdPartLoginMsg";

import type {
  LoginStoreState,
  PasswordLoginPayload,
  CodeLoginPayload,
  GeetestResult,
  LoginType,
  GeetestCallback,
  PasswordLoginParams,
  CodeLoginParams,
} from "@/types/login";

export const useLoginStore = defineStore("login", {
  state: (): LoginStoreState => ({
    userPhone: "",
    currentLoginMode: "password",
    isCodeInputMode: false,
    isPrivacyAgreed: true,
    isPrivacyDialogVisible: false,
    isLoggingIn: false,
  }),

  getters: {
    /**
     * 是否应该显示关闭按钮
     */
    shouldShowCloseButton(): boolean {
      return (
        this.currentLoginMode === "password" ||
        (this.currentLoginMode === "code" && !this.isCodeInputMode)
      );
    },

    /**
     * 是否应该显示返回按钮
     */
    shouldShowBackButton(): boolean {
      return this.isCodeInputMode;
    },

    /**
     * 是否有第三方登录选项
     */
    hasThirdPartyLogin(): boolean {
      const globalStore = useGlobalStore();
      return (
        globalStore.loginConfig.login_facebook == 0 || globalStore.loginConfig.login_google == 0
      );
    },
  },

  actions: {
    /**
     * 初始化登录页面
     */
    initLoginPage() {
      const globalStore = useGlobalStore();

      // 重置状态
      globalStore.loginOut(false);

      // 初始化第三方登录SDK
      this.initThirdPartySDK();

      // 恢复手机号
      const phoneNum = getLocalStorage("phone");
      if (phoneNum) {
        this.userPhone = phoneNum;
      }

      // 恢复登录模式
      const savedLoginMode = getLocalStorage("loginType");

      if (savedLoginMode && (savedLoginMode === "password" || savedLoginMode === "code")) {
        // 如果保存的是密码登录，但密码登录被关闭了，则使用验证码登录
        if (savedLoginMode === "password" && globalStore.loginConfig.login_password !== 0) {
          this.currentLoginMode = "code";
        } else {
          this.currentLoginMode = savedLoginMode;
        }
      } else {
        // 如果没有保存的登录模式，默认使用验证码登录
        this.currentLoginMode = "password";
      }
      // 重置下验证码登录的步骤
      this.isCodeInputMode = false;
    },

    /**
     * 初始化第三方登录SDK
     */
    initThirdPartySDK() {
      const globalStore = useGlobalStore();

      if (globalStore.loginConfig.login_facebook == 0) {
        LoginMgr.instance.facebook_init();
      }
      if (globalStore.loginConfig.login_google == 0) {
        LoginMgr.instance.google_init();
      }
    },

    /**
     * 处理验证 (支持 Geetest 和 Cloudflare)
     */
    async handleVerification(
      loginType: LoginType = "phone_login_code",
      callback: GeetestCallback
    ): Promise<void> {
      // 检查隐私协议
      if (!this.isPrivacyAgreed && ["phone_code_login", "password_login"].includes(loginType)) {
        this.isPrivacyDialogVisible = true;
        return;
      }

      try {
        // 使用统一的验证管理器
        await VerificationMgr.instance.verify(
          loginType,
          async (result) => {
            if (result && result.success) {
              // 根据验证类型处理结果
              if (result.type === VERIFICATION_TYPE.GEETEST) {
                // Geetest 验证结果
                await callback({
                  buds: result.data?.buds || "64",
                  geetest_guard: result.data?.geetest_guard || "",
                  userInfo: result.data?.userInfo || "",
                  geetest_captcha: result.data?.geetest_captcha || "",
                });
              } else if (result.type === VERIFICATION_TYPE.CLOUDFLARE) {
                // Cloudflare 验证结果 - 转换为 Geetest 格式以保持兼容性
                await callback({
                  // 添加 Cloudflare 特有的字段
                  "cf-token": result.data?.[`cf-token`] || result.data?.cf_token || "",
                  "cf-scene": result.data?.[`cf-scene`] || result.data?.cf_type || loginType,
                });
              }
            } else {
              console.warn("Verification failed or was cancelled");
              showToast("Verification failed");
            }
          },
          {
            phone: this.userPhone, // 传递手机号给 Geetest
          }
        );
      } catch (error) {
        console.error("Verification failed:", error);
        showToast("Verification failed");
      }
    },

    /**
     * @deprecated 使用 handleVerification 替代
     * 处理 Geetest 验证 (向后兼容方法)
     */
    async handleGeetestVerification(
      loginType: LoginType = "phone_login_code",
      callback: GeetestCallback
    ): Promise<void> {
      console.warn("⚠️ handleGeetestVerification is deprecated. Use handleVerification instead.");
      return this.handleVerification(loginType, callback);
    },

    /**
     * 执行用户登录
     */
    async executePlayerLogin(payload: PasswordLoginParams | CodeLoginParams): Promise<void> {
      try {
        this.isLoggingIn = true;
        showZLoading();
        await loginManager.executeLogin(payload);
      } catch (error) {
        console.error("Player login failed:", error);
        throw error;
      } finally {
        this.isLoggingIn = false;
        closeZLoading();
      }
    },

    /**
     * 处理密码登录
     */
    async handlePasswordLogin(payload: PasswordLoginPayload): Promise<void> {
      const { password, phone: phoneNum } = payload;

      if (!password) {
        throw new Error("Password is required");
      }

      return new Promise((resolve, reject) => {
        this.handleVerification("password_login", async (geetestResult: GeetestResult) => {
          try {
            const loginParams: PasswordLoginParams = {
              login_type: "phone",
              phone: phoneNum || this.userPhone,
              password: Md5.hashStr(password).toString(),
              ...geetestResult,
            };
            await this.executePlayerLogin(loginParams);
            resolve();
          } catch (error) {
            reject(error);
          }
        });
      });
    },

    /**
     * 处理验证码登录
     */
    async handleCodeLogin(payload: CodeLoginPayload): Promise<void> {
      const { verCode, phone: phoneNum } = payload;

      return new Promise((resolve, reject) => {
        this.handleVerification("phone_code_login", async (geetestResult: GeetestResult) => {
          try {
            const loginParams: CodeLoginParams = {
              login_type: "phone",
              phone: phoneNum || this.userPhone,
              verifyCode: verCode,
              ...geetestResult,
            };
            await this.executePlayerLogin(loginParams);
            resolve();
          } catch (error) {
            reject(error);
          }
        });
      });
    },

    /**
     * 处理Google登录
     */
    tapGoogleLogin() {
      LoginMgr.instance.google_login();
    },

    async handleGoogleLogin(idToken?) {
      const params = {
        googleToken: idToken,
        login_type: "google",
        google_redirect_uri: import.meta.env.VITE_WEB_URL,
        token: undefined,
      };
      await this.executePlayerLogin(params);
    },

    /**
     * 处理Facebook登录
     */
    tapFacebookLogin() {
      LoginMgr.instance.facebook_login();
    },
    async handleFacebookLogin(accessToken?, userID?) {
      const params = {
        accessToken: accessToken,
        faceUserId: userID,
        login_type: "facebook",
        token: undefined,
      };
      await this.executePlayerLogin(params);
    },

    /**
     * 切换到验证码登录
     */
    switchToCodeLogin(phoneNum: string) {
      this.userPhone = phoneNum;
      this.currentLoginMode = "code";
      setLocalStorage("loginType", "code");
    },

    /**
     * 切换到密码登录
     */
    switchToPasswordLogin(phoneNum: string) {
      this.userPhone = phoneNum;
      this.currentLoginMode = "password";
      setLocalStorage("loginType", "password");
    },

    /**
     * 处理验证码页面变化
     */
    handleCodePageChange(showCodeInput: boolean) {
      this.isCodeInputMode = showCodeInput;
    },

    /**
     * 处理返回操作
     */
    handleGoBack() {
      this.isCodeInputMode = false;
    },

    /**
     * 处理关闭页面
     */
    handleClosePage() {
      router.replace("/home");
      // router.back();
    },

    /**
     * 处理页面导航
     */
    handleNavigateToPage(path: string) {
      router.push(path);
    },

    /**
     * 处理隐私协议状态变化
     */
    handleAgreementChange(accepted: boolean) {
      this.isPrivacyAgreed = accepted;
    },

    /**
     * 处理隐私政策确认
     */
    handlePrivacyConfirm() {
      this.isPrivacyAgreed = true;
      this.isPrivacyDialogVisible = false;
    },

    /**
     * 重置登录状态
     */
    resetLoginState() {
      this.isCodeInputMode = false;
      this.isPrivacyDialogVisible = false;
      this.isLoggingIn = false;
    },
  },
});
