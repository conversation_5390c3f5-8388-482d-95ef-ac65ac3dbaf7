/**
 * Cloudflare Turnstile 验证管理器
 * 专注于弹窗验证，简化代码结构
 */

// CF Turnstile 验证场景类型 (保持与原版本兼容)
export enum CF_TURNSTILE_TYPE {
  /** 无 */
  NONE = "",
  /** 手机号注册登录-获取验证码 */
  LOGIN_PHONE_GET_CODE = "SCENE_GET_CODE",
  /** 登录-提交 */
  LOGIN_SUBMIT = "SCENE_LOGIN",
  /** 忘记密码-获取验证码 */
  FORGET_PW_GET_CODE = "SCENE_FORGET_PW_GET_CODE",
  /** 忘记密码-提交 */
  FORGET_PW_SUBMIT = "SCENE_FORGET_PASSWORD",
  /** 首次设定登录密码 */
  FIRST_SET_LOGIN_PW = "SCENE_FIRST_PASSWORD",
  /** 首次设定支付密码 */
  FIRST_SET_PAY_PW = "SCENE_FIRST_PAY_PASSWORD",
  /** 修改登录密码-获取验证码 */
  MODIFY_LOGIN_PW_GET_CODE = "SCENE_MODIFY_LOGIN_PW_GET_CODE",
  /** 修改登录密码-提交 */
  MODIFY_LOGIN_PW_SUBMIT = "SCENE_CHANGE_PASSWORD",
  /** 修改支付密码-获取验证码 */
  MODIFY_PAY_PW_GET_CODE = "xxx",
  /** 修改支付密码-提交 */
  MODIFY_PAY_PW_SUBMIT = "SCENE_CHANGE_PAY_PASSWORD",
  /** 绑定提款账号-获取验证码 */
  BIND_WITHDRAWAL_ACCOUNT_GET_CODE = "xxx",
  /** 绑定提款账号-提交 */
  BIND_WITHDRAWAL_ACCOUNT_SUBMIT = "SCENE_BIND_WITHDRAW_ACCOUNT",
  /** 修改提款账号-获取验证码 */
  MODIFY_WITHDRAWAL_ACCOUNT_GET_CODE = "xxx",
  /** 修改提款账号-提交 */
  MODIFY_WITHDRAWAL_ACCOUNT_SUBMIT = "SCENE_CHANGE_WITHDRAW_ACCOUNT",
  /** 提现-提交订单 */
  WITHDRAWAL_SUBMIT = "SCENE_WITHDRAW",
  /** 绑定手机号-获取验证码 */
  BIND_PHONE_GET_CODE = "xxx",
  /** 绑定手机号-提交 */
  BIND_PHONE_SUBMIT = "SCENE_BIND_PT_PHONE",
  /** 修改手机号-获取验证码 */
  MODIFY_PHONE_GET_CODE = "SCENE_MODIFY_PHONE_GET_CODE",
  /** 修改手机号-提交 */
  MODIFY_PHONE_SUBMIT = "SCENE_CHANGE_PT_PHONE",
  /** KYC 提交 */
  KYC_SUBMIT = "SCENE_SUB_KYC_INFO",
  /** 注册提交 */
  REGISTER_SUBMIT = "SCENE_REGISTER",
}

// Turnstile 验证结果接口
export interface TurnstileResult {
  success: boolean;
  "cf-token"?: string;
  "cf-scene": string;
  error?: string;
}

// Turnstile 配置接口
export interface TurnstileConfig {
  siteKey?: string;
  theme?: "light" | "dark" | "auto";
  size?: "normal" | "compact";
  language?: string;
  appearance?: "always" | "execute" | "interaction-only";
}

// 验证回调函数类型
export type VerifyCallback = (result: TurnstileResult | false) => void;

/**
 * Cloudflare Turnstile 管理器
 * 专注于弹窗验证，简化架构
 */
export class CloudflareMgr {
  private static _instance: CloudflareMgr;
  private isScriptLoaded = false;
  private loadingPromise: Promise<boolean> | null = null;

  // 单例模式
  static get instance(): CloudflareMgr {
    if (!this._instance) {
      this._instance = new CloudflareMgr();
    }
    return this._instance;
  }

  constructor() {
    this.initTurnstileScript();
  }

  /**
   * 初始化 Turnstile 脚本
   */
  private async initTurnstileScript(): Promise<boolean> {
    if (this.isScriptLoaded) {
      return true;
    }

    if (this.loadingPromise) {
      return this.loadingPromise;
    }

    this.loadingPromise = new Promise((resolve) => {
      // 检查是否已经加载
      if (window.turnstile) {
        this.isScriptLoaded = true;
        resolve(true);
        return;
      }

      // 创建脚本标签
      const script = document.createElement("script");
      script.src = "https://challenges.cloudflare.com/turnstile/v0/api.js";
      script.async = true;
      script.defer = true;

      script.onload = () => {
        console.log("✅ Turnstile script loaded successfully");
        this.isScriptLoaded = true;
        resolve(true);
      };

      script.onerror = () => {
        console.error("❌ Failed to load Turnstile script");
        this.loadingPromise = null;
        resolve(false);
      };

      document.head.appendChild(script);
    });

    return this.loadingPromise;
  }

  /**
   * 根据环境获取 Site Key
   */
  public getSiteKey(): string {
    const env = import.meta.env.MODE;
    let siteKey: string;

    // 优先检查本地开发环境的 Site Key
    const localSiteKey = import.meta.env.VITE_CF_SITE_KEY_LOCAL;
    if (localSiteKey && (env === "development" || process.env.NODE_ENV === "development")) {
      console.log("🏠 Using local development site key");
      siteKey = localSiteKey;
    } else {
      // 根据不同环境返回不同的 Site Key
      switch (env) {
        case "development":
          siteKey = import.meta.env.VITE_CF_SITE_KEY_DEV || "0x4AAAAAABnByxp2v1NuTm7f";
          break;
        case "test":
          siteKey = import.meta.env.VITE_CF_SITE_KEY_TEST || "0x4AAAAAABnByxp2v1NuTm7f";
          break;
        case "pre":
          siteKey = import.meta.env.VITE_CF_SITE_KEY_PRE || "0x4AAAAAABpKiQV8_G7FJy6p";
          break;
        case "production":
          siteKey = import.meta.env.VITE_CF_SITE_KEY_PROD || "0x4AAAAAABpKiQV8_G7FJy6p";
          break;
        default:
          siteKey = "0x4AAAAAABnByxp2v1NuTm7f"; // 默认测试环境 Site Key (总是通过)
      }
    }

    // 验证 Site Key 格式
    if (!this.validateSiteKey(siteKey)) {
      console.error("❌ Invalid Site Key format:", siteKey);
      throw new Error(`Invalid Site Key format: ${siteKey}`);
    }

    // 显示 Site Key 来源信息
    const source =
      localSiteKey && (env === "development" || process.env.NODE_ENV === "development")
        ? "VITE_CF_SITE_KEY_LOCAL"
        : `VITE_CF_SITE_KEY_${env.toUpperCase()}`;
    console.log(`🔑 Using site key for ${env} (${source}):`, siteKey);
    return siteKey;
  }

  /**
   * 验证 Site Key 格式
   */
  private validateSiteKey(siteKey: string): boolean {
    if (!siteKey || typeof siteKey !== "string") {
      return false;
    }

    // Cloudflare Site Key 格式支持:
    // 1. 生产环境: 0x4 开头的 Site Key
    // 2. 测试环境: 1x, 2x, 3x 开头的测试 Site Key
    const productionPattern = /^0x4[A-Za-z0-9_-]{20,25}$/;
    const testPattern = /^[1-3]x[A-Fa-f0-9]{20,25}$/;
    console.log(
      "验证 Site Key 格式: ",
      siteKey,
      productionPattern.test(siteKey),
      testPattern.test(siteKey)
    );
    return productionPattern.test(siteKey) || testPattern.test(siteKey);
  }

  /**
   * 渲染 Turnstile 验证组件
   * @param containerId 容器ID
   * @param cfType 验证类型
   * @param callback 回调函数
   * @param config 配置选项
   */
  public async renderTurnstile(
    containerId: string,
    cfType: CF_TURNSTILE_TYPE,
    callback: VerifyCallback,
    config?: TurnstileConfig
  ): Promise<string | null> {
    try {
      // 确保脚本已加载
      const scriptLoaded = await this.initTurnstileScript();
      if (!scriptLoaded) {
        throw new Error("Failed to load Turnstile script");
      }

      // 获取容器
      const container = document.getElementById(containerId);
      if (!container) {
        throw new Error(`Container with ID '${containerId}' not found`);
      }

      // 清空容器
      container.innerHTML = "";

      // 获取配置
      const siteKey = config?.siteKey || this.getSiteKey();

      // 构建 Turnstile 选项
      const turnstileOptions = {
        sitekey: siteKey,
        theme: config?.theme || "light",
        size: config?.size || "normal",
        language: config?.language || "en",
        appearance: config?.appearance || "always",
        callback: (token: string) => {
          console.log("🎉 Turnstile verification successful");
          const result: TurnstileResult = {
            success: true,
            "cf-token": token,
            "cf-scene": cfType,
          };
          callback(result);
        },
        "error-callback": (error: string) => {
          console.error("❌ Turnstile verification failed:", error);
          const result: TurnstileResult = {
            success: false,
            "cf-scene": cfType,
            error,
          };
          callback(result);
        },
        "expired-callback": () => {
          console.warn("⚠️ Turnstile verification expired");
          const result: TurnstileResult = {
            success: false,
            "cf-scene": cfType,
            error: "expired",
          };
          callback(result);
        },
      };

      // 渲染 Turnstile 组件
      const widgetId = window.turnstile.render(container, turnstileOptions);
      console.log("✅ Turnstile widget rendered successfully, ID:", widgetId);

      return widgetId;
    } catch (error) {
      console.error("❌ Failed to render Turnstile:", error);
      callback(false);
      return null;
    }
  }

  /**
   * 重置 Turnstile 组件
   */
  public reset(widgetId: string): void {
    if (widgetId && window.turnstile) {
      window.turnstile.reset(widgetId);
    }
  }

  /**
   * 移除 Turnstile 组件
   */
  public remove(widgetId: string): void {
    if (widgetId && window.turnstile) {
      window.turnstile.remove(widgetId);
    }
  }

  /**
   * 获取当前 token (兼容旧版本API)
   */
  getToken(): string {
    // 新版本的 token 在验证回调中返回，这里返回空字符串
    return "";
  }

  /**
   * 清除当前 token (兼容旧版本API)
   */
  clearToken(): void {
    // 新版本的 token 不持久化，无需清除
  }

  /**
   * 测试本地 Site Key 配置 (仅用于开发调试)
   */
  public testLocalSiteKeyConfig(): void {
    console.log("🧪 Testing local site key configuration...");

    const env = import.meta.env.MODE;
    const localSiteKey = import.meta.env.VITE_CF_SITE_KEY_LOCAL;
    const currentSiteKey = this.getSiteKey();

    console.log(`Environment: ${env}`);
    console.log(`VITE_CF_SITE_KEY_LOCAL: ${localSiteKey || "not set"}`);
    console.log(`Current site key: ${currentSiteKey}`);

    if (localSiteKey && env === "development") {
      console.log(
        currentSiteKey === localSiteKey
          ? "✅ Local site key is being used"
          : "❌ Local site key is NOT being used"
      );
    } else {
      console.log("ℹ️ Local site key not applicable for current environment");
    }

    console.log("🧪 Local site key configuration test completed.");
  }

  /**
   * 测试 Site Key 验证方法 (仅用于开发调试)
   */
  public testSiteKeyValidation(): void {
    const testCases = [
      // 测试环境 Site Keys (应该通过)
      "1x00000000000000000000AA", // 总是通过
      "2x00000000000000000000AB", // 总是失败
      "3x00000000000000000000FF", // 总是显示交互式挑战
      // 生产环境 Site Keys (应该通过)
      "0x4AAAAAAA12345678901234567",
      "0x4AAAAAAA98765432109876543",
      // 无效的 Site Keys (应该失败)
      "invalid-site-key",
      "0x3AAAAAAA12345678901234567", // 错误的前缀
      "4x00000000000000000000AA", // 错误的测试前缀
      "",
      null as any,
      undefined as any,
    ];

    console.log("🧪 Testing Site Key validation...");
    testCases.forEach((siteKey, index) => {
      const isValid = this.validateSiteKey(siteKey);
      const status = isValid ? "✅ PASS" : "❌ FAIL";
      console.log(`Test ${index + 1}: ${status} - "${siteKey}"`);
    });
    console.log("🧪 Site Key validation test completed.");
  }
}

// 声明全局 turnstile 对象
declare global {
  interface Window {
    turnstile: {
      render: (container: HTMLElement, options: any) => string;
      reset: (widgetId: string) => void;
      remove: (widgetId: string) => void;
      getResponse: (widgetId: string) => string;
    };
  }
}
