<template>
  <div class="turnstile-example">
    <h2>Simple Turnstile Integration Example</h2>

    <!-- Basic usage -->
    <div class="example-section">
      <h3>Basic Form with Turnstile</h3>
      <form @submit.prevent="handleSubmit" class="example-form">
        <div class="form-field">
          <label for="username">Username:</label>
          <input
            id="username"
            v-model="form.username"
            type="text"
            required
            placeholder="Enter username"
          />
        </div>

        <div class="form-field">
          <label for="password">Password:</label>
          <input
            id="password"
            v-model="form.password"
            type="password"
            required
            placeholder="Enter password"
          />
        </div>

        <div class="form-field">
          <label>Security Verification:</label>
          <TurnstileWidget
            ref="turnstileRef"
            :site-key="siteKey"
            theme="light"
            size="normal"
            @success="onTurnstileSuccess"
            @error="onTurnstileError"
            @expired="onTurnstileExpired"
          />
        </div>

        <button type="submit" :disabled="!isVerified || isSubmitting" class="submit-button">
          {{ isSubmitting ? "Logging in..." : "Login" }}
        </button>
      </form>
    </div>

    <!-- Status display -->
    <div class="example-section">
      <h3>Verification Status</h3>
      <div class="status-info">
        <p><strong>Verified:</strong> {{ isVerified ? "✅ Yes" : "❌ No" }}</p>
        <p v-if="verificationToken">
          <strong>Token:</strong> {{ verificationToken.substring(0, 30) }}...
        </p>
        <p v-if="errorMsg" class="error"><strong>Error:</strong> {{ errorMsg }}</p>
      </div>

      <div class="action-buttons">
        <button @click="resetTurnstile" class="action-btn">Reset Turnstile</button>
        <button @click="clearForm" class="action-btn">Clear Form</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import TurnstileWidget from "./TurnstileWidget.vue";

// Form data
const form = ref({
  username: "",
  password: "",
});

// Turnstile configuration
const siteKey = "1x00000000000000000000AA"; // Cloudflare test site key (always passes)

// State
const isVerified = ref(false);
const verificationToken = ref("");
const errorMsg = ref("");
const isSubmitting = ref(false);

// Component reference
const turnstileRef = ref<InstanceType<typeof TurnstileWidget>>();

// Turnstile event handlers
const onTurnstileSuccess = (token: string) => {
  console.log("Turnstile verification successful:", token);
  isVerified.value = true;
  verificationToken.value = token;
  errorMsg.value = "";
};

const onTurnstileError = (error: string) => {
  console.error("Turnstile verification failed:", error);
  isVerified.value = false;
  verificationToken.value = "";
  errorMsg.value = error;
};

const onTurnstileExpired = () => {
  console.warn("Turnstile verification expired");
  isVerified.value = false;
  verificationToken.value = "";
  errorMsg.value = "Verification expired, please try again";
};

// Form submission
const handleSubmit = async () => {
  if (!isVerified.value) {
    alert("Please complete the security verification first");
    return;
  }

  isSubmitting.value = true;

  try {
    // Simulate API call
    console.log("Submitting form with:", {
      username: form.value.username,
      password: "***",
      turnstileToken: verificationToken.value,
    });

    // Simulate network delay
    await new Promise((resolve) => setTimeout(resolve, 1500));

    alert("Login successful!");
    clearForm();
  } catch (error) {
    console.error("Login failed:", error);
    alert("Login failed. Please try again.");
  } finally {
    isSubmitting.value = false;
  }
};

// Reset Turnstile
const resetTurnstile = () => {
  if (turnstileRef.value) {
    turnstileRef.value.reset();
  }
  isVerified.value = false;
  verificationToken.value = "";
  errorMsg.value = "";
};

// Clear form
const clearForm = () => {
  form.value.username = "";
  form.value.password = "";
  resetTurnstile();
};
</script>

<style scoped>
.turnstile-example {
  max-width: 500px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.turnstile-example h2 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
}

.example-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #f9f9f9;
}

.example-section h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #555;
  font-size: 18px;
}

.example-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-field label {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.form-field input {
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 16px;
}

.form-field input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.submit-button {
  padding: 12px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-top: 10px;
}

.submit-button:hover:not(:disabled) {
  background: #0056b3;
}

.submit-button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.status-info {
  margin-bottom: 16px;
}

.status-info p {
  margin: 8px 0;
  font-size: 14px;
}

.status-info .error {
  color: #dc3545;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.action-btn {
  padding: 8px 16px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-btn:hover {
  background: #545b62;
}
</style>
